# 自律守护者 v7.0 (融合版) - 项目概述

## 1. 设计哲学

**自律守护者 v7.0** 是一款为强意志力用户设计的终极自我约束工具。它并非一个简单的进程杀手，而是一个深度集成到系统的、难以规避的数字化契约。其核心设计哲学包括：

*   **统一守护 (Unified Core):** 整个系统由一个单一的、高权限的Windows服务驱动，集成了所有监控与保护功能，并具备强大的自我修复能力。
*   **双层过滤 (Dual-Layer Filtering):** 提供“全局进程扫描”和“焦点内容监控”两层独立的过滤系统，既能宏观封锁应用，也能微观审查内容。
*   **AI赋能 (AI-Powered):** 引入文本和视觉AI，实现对浏览器内容的智能审核和对未知应用程序的自动分类，让保护策略动态生长。
*   **契约精神 (The Contract):** 严格遵守预设的“卸载窗口期”。我们尊重用户的最终选择权，但坚决对抗冲动性放弃，在非窗口期内，守护不可动摇。
*   **高墙壁垒 (Fortress-like Defense):** 通过身份伪装、安全启动、权限锁定等手段，大幅提高在常规操作下被关闭或破坏的技术门槛。

---

## 2. 核心功能

1.  **全局进程扫描 (Global Process Scan):**
    *   继承自项目一的经典功能。
    *   根据用户设定的进程黑名单（如 `game.exe`）和时间段，在后台持续查杀所有匹配的进程，无论其是否可见。

2.  **焦点内容监控 (Focus Content Monitor):**
    *   继承自项目二的智能监控能力。
    *   仅针对用户当前聚焦的浏览器窗口，通过读取URL和标题进行精细化内容过滤。
    *   支持基于**关键词/URL黑名单**和**AI内容审核**的双重判断。

3.  **AI驱动的浏览器内容审核 (AI-Powered Content Audit):**
    *   当用户浏览网页时，自动将URL和标题发送给AI进行合规性判断。
    *   若AI判定内容违规（如游戏、色情、暴力），系统将**自动关闭该浏览器标签页**。

4.  **智能视觉分析与自动分类 (Visual AI Analysis & Auto-Classification):**
    *   **核心创新功能**。持续监控所有前台应用的累计使用时长。
    *   当任何陌生应用的累计使用时长超过阈值（如30分钟），系统会自动**截图**并交由视觉AI进行分析。
    *   AI负责将应用分类为“游戏”、“工具”、“聊天软件”等，并永久记录。

5.  **基于分类的时间管理 (Category-Based Time Management):**
    *   一旦应用被AI分类，将自动受到该分类的时间限制规则约束。
    *   用户可为每个类别设定每日总使用时长上限（如“游戏”类每天1小时）。
    *   当某类别应用使用总时长超限时，系统将**自动强制关闭**该应用。

6.  **拒绝后的交互与临时豁免 (Post-Denial Interaction & Override):**
    *   当AI关闭一个网页或应用时，系统会立即弹出通知。
    *   通知窗口提供**“强制通过”**选项，允许用户将该URL或应用加入临时白名单（有效期至下一个“卸载窗口期”）。
    *   *注意：根据蓝图设计，此功能应有使用次数限制。*

---

## 3. 系统架构

系统围绕一个统一的后台服务 `unified_guardian_service.py` 构建。该服务内部包含多个可按需启动的、独立的工作线程，并由一个统一的配置中心 (`config.dat`) 和数据中心 (`database.db`) 提供支持。

*   **`GuardianCore`**: 核心逻辑类，负责管理所有工作线程。
    *   **全局进程扫描线程**: 执行进程黑名单查杀。
    *   **焦点内容监控线程**: 执行浏览器内容审查。
    *   **智能视觉分析线程**: 执行应用使用时长统计、AI分类和超时查杀。
*   **`common` 模块**: 提供加密、配置、数据库、通知、时间、系统和进程管理等所有共享的核心工具。
*   **`intelligent_monitor` 模块**: 提供高级的浏览器UI自动化能力，包括自适应的URL获取和标签页关闭策略。
*   **`frontend` 模块**: 提供基于Web技术的用户控制面板 (`web_control_panel.py`) 和安全启动器 (`launcher.py`)。

---

## 4. 关键数据结构

### 4.1. 统一配置文件 (`config.dat`)

这是一个被加密的核心文件，定义了系统的所有行为。其主要结构如下：

```json
{
    "features": {
        "enable_global_process_scan": true,
        "enable_focus_content_monitor": true,
        "enable_vision_analysis": true
    },
    "rules": {
        "process_blacklist": [ "game.exe,20:00,22:00" ],
        "browser_content_blacklist": {
            "keywords": ["游戏"],
            "urls": ["bad-website.com"]
        },
        "time_limit_rules": {
            "游戏": {"limit_minutes_per_day": 60, "enabled": true}
        }
    },
    "ai_config": {
        "text_audit": { "enabled": true, "provider": "openai", ... },
        "vision_audit": { "enabled": true, "provider": "openai", ... }
    },
    "uninstall_window": { ... }
}
```

### 4.2. 数据库 (`database.db`)

这是一个SQLite数据库，用于存储动态生成的数据。

*   **`app_classification` 表**: 存储视觉AI对应用的分类结果。
    *   `process_name` (TEXT, PK): 进程名。
    *   `category` (TEXT): AI分类结果。

*   **`focus_log` 表**: 记录每个应用的专注使用时长。
    *   `process_name` (TEXT): 进程名。
    *   `duration_seconds` (INTEGER): 本次专注时长。

*   **`temp_whitelist` 表**: 用于“强制通过”功能。
    *   `value` (TEXT, UNIQUE): 被豁免的URL或进程名。
    *   `expiry_timestamp` (INTEGER): 豁免过期时间戳。

---

## 5. 如何使用

1.  **安装**: 以管理员身份运行 `installer/setup.exe`，并根据提示提供安装凭证。
2.  **配置**: 安装完成后，通过桌面快捷方式打开“自律守护者控制面板”。
    *   在**规则管理**中添加要封禁的进程黑名单。
    *   在**AI与自动化**中配置您的AI服务API密钥以启用智能审核功能。
    *   在**时间限制**中为不同应用类别设置每日使用上限。
    *   在**系统设置**中配置“卸载窗口期”。
3.  **运行**: 配置完成后，守护服务将在后台自动运行，执行您设定的规则。