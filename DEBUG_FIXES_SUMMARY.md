# 自律守护者 v7.0 (融合版) - DEBUG修复说明

**版本:** 1.0
**日期:** 2025年7月16日
**审查人:** Roo

## 1. 总体评价

项目整体代码质量优秀，架构设计合理，功能实现完整度非常高，与《融合蓝图.md》中定义的设计目标和架构高度一致。以下指出的问题是在高标准要求下，与蓝图设计细节的轻微偏差，属于功能待完善部分，而非严重bug。

---

## 2. 待修复问题清单

### 问题1：临时豁免功能未完全实现蓝图要求

*   **问题模块**: 通知管理器 (`NotificationManager`)
*   **相关文件**: [`common/notification_manager.py:177-180`](common/notification_manager.py:177)
*   **蓝图要求**:
    > 《融合蓝图.md》第43-44行: "点击后，该被拒绝的URL将被加入一个临时的白名单，有效期为24小时。在此期间，再次访问该URL将不会被拦截。**强制通过有次数限制，时间段为当前到下一个窗口期。**"

*   **当前实现**:
    当前代码在用户点击“强制通过”后，调用 `self.database_manager.add_to_temp_whitelist("url", url, 24)`，将URL加入临时白名单，固定有效期为24小时。

*   **逻辑缺陷**:
    1.  **缺少次数限制**: 当前实现允许用户在窗口期之间无限次使用“强制通过”功能。
    2.  **豁免有效期不准确**: 豁免有效期被硬编码为24小时，而不是动态计算到“下一个窗口期的开始时间”。

*   **潜在影响**:
    此功能的简化实现削弱了项目的核心“契约精神”。用户可以通过连续使用“强制通过”来绕过内容审核，使得AI审核形同虚设。

*   **修复建议**:

    1.  **数据库层面**:
        *   在 `temp_whitelist` 表中增加一个 `usage_count` 字段来记录使用次数，或者创建一个新表来跟踪豁免次数。
        *   在 `config.dat` 的 `uninstall_window` 部分增加一个 `override_limit_per_cycle` 字段，用于配置每个周期内允许的豁免次数（例如：3次）。

    2.  **逻辑层面 (在 `common/notification_manager.py` 和 `backend/unified_guardian_service.py` 中)**:
        *   **增加次数检查**: 在 `show_ai_rejection_notification` 方法中，当用户点击“强制通过”时，需要先从数据库查询当前周期内的使用次数，如果超过限制，则弹窗告知用户“本周期豁免次数已用尽”，并且不添加白名单。
        *   **动态计算有效期**:
            *   需要一个能计算“下一个窗口期开始时间戳”的辅助函数，可以放在 [`common/time_utils.py`](common/time_utils.py:1) 中。
            *   在调用 `add_to_temp_whitelist` 时，不再传入固定的 `24` 小时，而是传入计算出的到下一个窗口期开始的剩余小时数。
        *   **重置豁免次数**: 在守护服务 `GuardianCore` 的主循环或一个定时任务中，需要判断当前时间是否进入了新的“卸载窗口期”。一旦进入，就自动清零或重置豁免使用次数的记录。

---

## 3. 总结

除上述问题外，项目其余部分的实现均符合或超出了蓝图的预期。修复此问题将使项目的核心保护逻辑更加严密，完全符合“钢铁契约”的设计哲学。
